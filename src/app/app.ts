import {Component, computed, effect, signal} from '@angular/core';
import {HttpErrorResponse, httpResource} from '@angular/common/http';
import {Todo} from '../model/Todo';
import {JsonPipe} from '@angular/common';

@Component({
  selector: 'app-root',
  imports: [
    JsonPipe
  ],
  templateUrl: './app.html',
  styleUrl: './app.scss'
})
export class App {
  private TODOS_URL = "https://jsonplaceholder.typicode.com/todos"
  index = signal(1)

  constructor() {
    effect(() => {
      console.log(typeof this.todos())
    })
  }

  /*http = httpResource<Todo[]>(() => this.TODOS_URL)*/
  http = httpResource<Todo[]>(() => ({
    url: `${this.TODOS_URL}/${this.index()}`,
    timeout: 1000,
    reportProgress: true,
  }))

  todos = computed(() => this.http.value())
  complete = computed(() => this.http.hasValue())
  loading = computed(() => this.http.isLoading())
  error = computed(() => this.http.error() as HttpErrorResponse)
  progress = computed(() => this.http.progress())
}
