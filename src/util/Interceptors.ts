import {HttpHandlerFn, HttpRequest} from '@angular/common/http';

export const interceptor = (request: HttpRequest<unknown>, handler: HttpHandlerFn) => {
  console.log('intercept request: ', request.url)

  // Read/inspect headers from the request
  const userAgent = request.headers.get('User-Agent');
  const host = request.headers.get('Host');
  const referer = request.headers.get('Referer');

  console.log('User-Agent:', userAgent);
  console.log('Host:', host);
  console.log('Referer:', referer);
  console.log('All headers:', request.headers.keys());

  // Log all headers
  request.headers.keys().forEach(key => {
    console.log(`${key}: ${request.headers.get(key)}`);
  });

  return handler(request);
};

